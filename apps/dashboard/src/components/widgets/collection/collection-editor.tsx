import { safQ<PERSON>y } from "@/client"
import { Field } from "@/components/common/field"
import { Form } from "@/components/common/form"
import { Loader } from "@/components/common/loader"
import { Options } from "@/configs"
import { createEnumFromOptions } from "@/lib/conversion"
import { cn, resolveNumberOrUndefined } from "@/lib/utils"
import { zodResolver } from "@hookform/resolvers/zod"
import { components } from "@saf/sdk"
import { Button, Checkbox, Input, Select, Text } from "@saf/ui"
import { useState } from "react"
import { useForm } from "react-hook-form"
import { useParams } from "react-router-dom"
import { useDeepCompareEffect } from "use-deep-compare"
import { z } from "zod"
import { generalWidgetEditorSchema, GeneralWidgetFields } from "../general"
import { inlineInputWidth } from "../utils/constant"
import { FieldSelectionInput } from "../utils/field-selection-input"
import { IconCode } from "@tabler/icons-react"
import { ResponsePreviewPanel } from "./response-preview-panel"

const styleOptions: Options = [
  { label: "List", value: "list" },
  { label: "Grid", value: "grid" },
]

const sizeOptions: Options = [
  { label: "Default", value: "default" },
  { label: "Compact", value: "compact" },
]

const designStyleOptions: Options = [
  { label: "Default", value: "default" },
  { label: "Card", value: "card" },
]

const imageShapeOptions: Options = [
  { label: "Circle", value: "circle" },
  { label: "Square", value: "square" },
]

const collectionEditorSchema = generalWidgetEditorSchema.merge(
  z.object({
    dataSourceId: z.string().optional(),
    externalApiId: z.string().optional(),
    rootArrayPath: z.string().optional(),
    style: z.enum(createEnumFromOptions(styleOptions)),
    titleField: z.string().optional(),
    subtitleField: z.string().optional(),
    metaField: z.string().optional(),
    imageField: z.string().optional(),
    size: z.enum(createEnumFromOptions(sizeOptions)),
    designStyle: z.enum(createEnumFromOptions(designStyleOptions)),
    imageShape: z.enum(createEnumFromOptions(imageShapeOptions)),
    limitItems: z.coerce.number().min(1).optional().or(z.literal("")),
    paginationEnabled: z.boolean(),
    responsePage: z.string().optional(),
    responseLimit: z.string().optional(),
    requestPage: z.string().optional(),
    requestLimit: z.string().optional(),
    paginationSize: z.coerce.number().min(1).optional().or(z.literal("")),
  }),
)

type CollectionEditorSchema = z.infer<typeof collectionEditorSchema>

export const CollectionEditor = ({
  data,
  onUpdate,
}: {
  data: components["schemas"]["CollectionWidget"]
  onUpdate: (updatedData: components["schemas"]["CollectionWidget"]) => void
}) => {
  const [initialRender, setInitialRender] = useState(true)
  const { teamId = "", miniAppId = "" } = useParams()
  const [openResponsePreview, setOpenResponsePreview] = useState(false)

  const form = useForm<CollectionEditorSchema>({
    mode: "onChange",
    resolver: zodResolver(collectionEditorSchema),
    defaultValues: {
      name: "",
      isHidden: false,
      dataSourceId: "defaultValue",
      externalApiId: "",
      rootArrayPath: "",
      style: "list",
      titleField: "",
      subtitleField: "",
      metaField: "",
      imageField: "",
      size: "default",
      designStyle: "default",
      imageShape: "circle",
      limitItems: "",
      paginationEnabled: false,
      responsePage: "",
      responseLimit: "",
      requestPage: "",
      requestLimit: "",
      paginationSize: "",
    },
  })

  const { data: dataSourcesData, isLoading: isLoadingDataSources } = safQuery.useQuery(
    "get",
    "/api/admin/teams/{teamId}/mini-apps/{miniAppId}/data-sources",
    {
      params: {
        path: {
          teamId: parseInt(teamId),
          miniAppId: parseInt(miniAppId),
        },
        query: {
          page: "1",
          limit: "200",
        },
      },
    },
  )

  const selectedDataSourceId = form.watch("dataSourceId")

  const { data: externalApisData, isLoading: isLoadingExernalApi } = safQuery.useQuery(
    "get",
    "/api/admin/teams/{teamId}/mini-apps/{miniAppId}/data-sources/{dataSourceId}/external-apis",
    {
      params: {
        path: {
          teamId: parseInt(teamId),
          miniAppId: parseInt(miniAppId),
          dataSourceId: parseInt(selectedDataSourceId ?? ""),
        },
        query: {
          page: "1",
          limit: "200",
        },
      },
    },
    {
      enabled: teamId != "" && miniAppId != "" && selectedDataSourceId != "" && selectedDataSourceId != null,
    },
  )

  const selectedExternalApiId = form.watch("externalApiId")

  const { data: externalApiResponseData, isLoading: isLoadingExternalApiResponseData } = safQuery.useQuery(
    "get",
    "/api/admin/teams/{teamId}/mini-apps/{miniAppId}/data-sources/{dataSourceId}/external-apis/{externalApiId}/execute",
    {
      params: {
        path: {
          teamId: parseInt(teamId),
          miniAppId: parseInt(miniAppId),
          dataSourceId: parseInt(selectedDataSourceId ?? ""),
          externalApiId: parseInt(selectedExternalApiId ?? ""),
        },
      },
    },
    {
      enabled:
        teamId != "" &&
        miniAppId != "" &&
        selectedDataSourceId != "" &&
        selectedDataSourceId != null &&
        selectedExternalApiId != "" &&
        selectedExternalApiId != null,
    },
  )

  useDeepCompareEffect(() => {
    if (data && initialRender) {
      form.reset({
        name: data.name || "",
        isHidden: data.isHidden ?? false,
        dataSourceId: data.config.bindingConfig?.dataSourceId?.toString() || undefined,
        externalApiId: data.config.bindingConfig?.externalApiId?.toString() || undefined,
        rootArrayPath: data.config.rootArrayPath || "",
        style: data.config.style || "list",
        titleField: data.config.itemsData?.title || "",
        subtitleField: data.config.itemsData?.subtitle || "",
        metaField: data.config.itemsData?.meta || "",
        imageField: data.config.itemsData?.image || "",
        size: data.config.design?.size || "default",
        designStyle: data.config.design?.style || "default",
        imageShape: data.config.design?.imageShape || "circle",
        limitItems: data.config.options?.limitItems || undefined,
        paginationEnabled: data.config.pagination?.enabled ?? false,
        requestPage: data.config.pagination?.requestPage || "",
        requestLimit: data.config.pagination?.requestLimit || "",
        paginationSize: data.config.pagination?.size || undefined,
      })
      setInitialRender(false)
    }
  }, [data, form, initialRender])

  useDeepCompareEffect(() => {
    if (!data) return

    return form.subscribe({
      name: "",
      formState: {
        values: true,
      },
      callback({ values }) {
        const updatedData: components["schemas"]["CollectionWidget"] = {
          ...data,
          name: values.name || "",
          isHidden: values.isHidden ?? false,
          config: {
            ...data.config,
            bindingConfig: {
              dataSourceId: values.dataSourceId != null ? parseInt(values.dataSourceId) : undefined,
              externalApiId: values.externalApiId != null ? parseInt(values.externalApiId) : undefined,
            },
            rootArrayPath: values.rootArrayPath || "",
            style: values.style as components["schemas"]["CollectionWidget"]["config"]["style"],
            itemsData: {
              title: values.titleField || "",
              subtitle: values.subtitleField || "",
              meta: values.metaField || "",
              image: values.imageField || "",
            },
            design: {
              size: values.size as components["schemas"]["CollectionWidget"]["config"]["design"]["size"],
              style: values.designStyle as components["schemas"]["CollectionWidget"]["config"]["design"]["style"],
              imageShape:
                values.imageShape as components["schemas"]["CollectionWidget"]["config"]["design"]["imageShape"],
            },
            options: {
              limitItems: resolveNumberOrUndefined(values.limitItems),
            },
            pagination: {
              enabled: values.paginationEnabled ?? false,
              requestPage: values.requestPage || "",
              requestLimit: values.requestLimit || "",
              size: resolveNumberOrUndefined(values.paginationSize),
            },
          },
        }
        onUpdate(updatedData)
      },
    })
  }, [data, form, onUpdate])

  const dataSourceOptions =
    dataSourcesData?.items?.map((ds) => ({
      label: ds.name,
      value: ds.id.toString(),
    })) || []

  const externalApiOptions =
    externalApisData?.items?.map((api) => ({
      label: api.name,
      value: api.id.toString(),
    })) || []

  return (
    <Form {...form}>
      {openResponsePreview && (
        <div className="absolute inset-y-0 left-0 z-50 w-1/3">
          <Button
            variant="secondary"
            className="absolute right-6 top-2 z-50"
            onClick={() => setOpenResponsePreview(false)}
          >
            Close Preview
          </Button>
          <ResponsePreviewPanel data={externalApiResponseData?.data} />
        </div>
      )}
      <form className="divide-y">
        <GeneralWidgetFields />

        <div className="flex flex-col gap-4 p-4">
          <Text className="uppercase" size="xsmall" weight="plus">
            Data Binding
          </Text>

          <Form.Field
            control={form.control}
            name="dataSourceId"
            render={({ field: { ref, onChange, ...field } }) => {
              return (
                <Form.Item className="flex-row justify-between gap-4">
                  <Form.Label className="flex items-center gap-2">
                    Data Source
                    {isLoadingDataSources && <Loader size="small" />}
                  </Form.Label>
                  <Form.Control>
                    <Select
                      {...field}
                      onValueChange={(value) => {
                        if (value == null || value == "") return
                        onChange(value)
                        form.setValue("externalApiId", undefined)
                      }}
                    >
                      <Select.Trigger ref={ref} className={inlineInputWidth}>
                        <Select.Value placeholder="Select data source..." />
                      </Select.Trigger>
                      <Select.Content>
                        {dataSourceOptions.map((item) => (
                          <Select.Item key={item.value} value={item.value}>
                            {item.label}
                          </Select.Item>
                        ))}
                      </Select.Content>
                    </Select>
                  </Form.Control>
                  <Form.ErrorMessage />
                </Form.Item>
              )
            }}
          />

          {selectedDataSourceId != null && (
            <div className="flex flex-col gap-2">
              <Form.Field
                control={form.control}
                name="externalApiId"
                render={({ field: { ref, onChange, ...field } }) => {
                  return (
                    <Form.Item className="flex-row justify-between gap-4">
                      <Form.Label className="flex items-center gap-2">
                        External API
                        {isLoadingExernalApi && <Loader size="small" />}
                      </Form.Label>

                      <Form.Control>
                        <Select
                          {...field}
                          onValueChange={(value) => {
                            if (value == null || value == "") return
                            onChange(value)
                          }}
                        >
                          <Select.Trigger ref={ref} className={inlineInputWidth}>
                            <Select.Value placeholder="Select external API..." />
                          </Select.Trigger>
                          <Select.Content>
                            {externalApiOptions.map((item) => (
                              <Select.Item key={item.value} value={item.value}>
                                {item.label}
                              </Select.Item>
                            ))}
                          </Select.Content>
                        </Select>
                      </Form.Control>
                      <Form.ErrorMessage />
                    </Form.Item>
                  )
                }}
              />
              {selectedExternalApiId != null && (
                <Button
                  size="small"
                  variant="secondary"
                  type="button"
                  onClick={() => {
                    setOpenResponsePreview(!openResponsePreview)
                  }}
                  className={cn(inlineInputWidth, "self-end")}
                  disabled={isLoadingExternalApiResponseData}
                >
                  {isLoadingExernalApi && isLoadingExternalApiResponseData && <Loader size="small" />}
                  <IconCode className="size-4" />
                  Preview
                </Button>
              )}
            </div>
          )}

          {form.watch("externalApiId") != null && (
            <Form.Field
              control={form.control}
              name="rootArrayPath"
              render={({ field }) => {
                return (
                  <Form.Item>
                    <Form.Label inline>
                      List Path
                      <Form.Control>
                        <FieldSelectionInput
                          placeholder="e.g., data.items"
                          schema={externalApiResponseData?.schema}
                          {...field}
                          className={inlineInputWidth}
                          onChange={(value) => {
                            if (value == null || value == "") return
                            // For rootArrayPath, we don't want the liquid template format
                            // Extract the path if it's in liquid format
                            const path = value.replace(/{{|}}/g, "").trim()
                            field.onChange(path)

                            // Reset field selections when root path changes
                            form.setValue("titleField", "")
                            form.setValue("subtitleField", "")
                            form.setValue("metaField", "")
                            form.setValue("imageField", "")
                          }}
                        />
                      </Form.Control>
                    </Form.Label>
                    <Form.ErrorMessage />
                  </Form.Item>
                )
              }}
            />
          )}
        </div>

        <div className="flex flex-col gap-4 p-4">
          <Text className="uppercase" size="xsmall" weight="plus">
            Layout
          </Text>

          <Form.Field
            control={form.control}
            name="style"
            render={({ field: { ref, onChange, ...field } }) => {
              return (
                <Form.Item>
                  <Form.Label inline>
                    Style
                    <Form.Control>
                      <Select {...field} onValueChange={onChange}>
                        <Select.Trigger ref={ref} className={inlineInputWidth}>
                          <Select.Value />
                        </Select.Trigger>
                        <Select.Content>
                          {styleOptions.map((item) => (
                            <Select.Item key={item.value} value={item.value}>
                              {item.label}
                            </Select.Item>
                          ))}
                        </Select.Content>
                      </Select>
                    </Form.Control>
                  </Form.Label>
                  <Form.ErrorMessage />
                </Form.Item>
              )
            }}
          />
        </div>

        <div className="flex flex-col gap-4 p-4">
          <Text className="uppercase" size="xsmall" weight="plus">
            Field Mapping
          </Text>

          <Field control={form.control} name="titleField" label="Title">
            <FieldSelectionInput
              placeholder="e.g., title, name..."
              schema={externalApiResponseData?.schema}
              rootArrayPath={form.watch("rootArrayPath")}
            />
          </Field>

          <Field control={form.control} name="subtitleField" label="Subtitle">
            <FieldSelectionInput
              placeholder="e.g., subtitle, description..."
              schema={externalApiResponseData?.schema}
              rootArrayPath={form.watch("rootArrayPath")}
            />
          </Field>

          <Field control={form.control} name="metaField" label="Meta">
            <FieldSelectionInput
              placeholder="e.g., category, type..."
              schema={externalApiResponseData?.schema}
              rootArrayPath={form.watch("rootArrayPath")}
            />
          </Field>

          <Field control={form.control} name="imageField" label="Image">
            <FieldSelectionInput
              placeholder="e.g., image, thumbnail..."
              schema={externalApiResponseData?.schema}
              rootArrayPath={form.watch("rootArrayPath")}
            />
          </Field>
        </div>

        <div className="flex flex-col gap-4 p-4">
          <Text className="uppercase" size="xsmall" weight="plus">
            Design
          </Text>
          <div className="space-y-4">
            <Form.Field
              control={form.control}
              name="size"
              render={({ field: { ref, onChange, ...field } }) => {
                return (
                  <Form.Item>
                    <Form.Label inline>
                      Size
                      <Form.Control>
                        <Select {...field} onValueChange={onChange}>
                          <Select.Trigger ref={ref} className={inlineInputWidth}>
                            <Select.Value />
                          </Select.Trigger>
                          <Select.Content>
                            {sizeOptions.map((item) => (
                              <Select.Item key={item.value} value={item.value}>
                                {item.label}
                              </Select.Item>
                            ))}
                          </Select.Content>
                        </Select>
                      </Form.Control>
                    </Form.Label>
                    <Form.ErrorMessage />
                  </Form.Item>
                )
              }}
            />

            <Form.Field
              control={form.control}
              name="designStyle"
              render={({ field: { ref, onChange, ...field } }) => {
                return (
                  <Form.Item>
                    <Form.Label inline>
                      Style
                      <Form.Control>
                        <Select {...field} onValueChange={onChange}>
                          <Select.Trigger ref={ref} className={inlineInputWidth}>
                            <Select.Value />
                          </Select.Trigger>
                          <Select.Content>
                            {designStyleOptions.map((item) => (
                              <Select.Item key={item.value} value={item.value}>
                                {item.label}
                              </Select.Item>
                            ))}
                          </Select.Content>
                        </Select>
                      </Form.Control>
                    </Form.Label>
                    <Form.ErrorMessage />
                  </Form.Item>
                )
              }}
            />

            <Form.Field
              control={form.control}
              name="imageShape"
              render={({ field: { ref, onChange, ...field } }) => {
                return (
                  <Form.Item>
                    <Form.Label inline>
                      Image Shape
                      <Form.Control>
                        <Select {...field} onValueChange={onChange}>
                          <Select.Trigger ref={ref} className={inlineInputWidth}>
                            <Select.Value />
                          </Select.Trigger>
                          <Select.Content>
                            {imageShapeOptions.map((item) => (
                              <Select.Item key={item.value} value={item.value}>
                                {item.label}
                              </Select.Item>
                            ))}
                          </Select.Content>
                        </Select>
                      </Form.Control>
                    </Form.Label>
                    <Form.ErrorMessage />
                  </Form.Item>
                )
              }}
            />
          </div>
        </div>

        <div className="flex flex-col gap-4 p-4">
          <Text className="uppercase" size="xsmall" weight="plus">
            Options
          </Text>

          <Form.Field
            control={form.control}
            name="limitItems"
            render={({ field }) => {
              return (
                <Form.Item>
                  <Form.Label inline>
                    Limit Items
                    <Form.Control>
                      <Input type="number" placeholder="e.g., 10, 20..." {...field} className={inlineInputWidth} />
                    </Form.Control>
                  </Form.Label>
                  <Form.ErrorMessage />
                </Form.Item>
              )
            }}
          />
        </div>

        <div className="flex flex-col gap-4 p-4">
          <Text className="uppercase" size="xsmall" weight="plus">
            Pagination
          </Text>

          <Form.Field
            control={form.control}
            name="paginationEnabled"
            render={({ field: { value, onChange, ...field } }) => {
              return (
                <Form.Item className="flex flex-row items-center gap-3">
                  <Form.Control>
                    <Checkbox checked={value} onCheckedChange={onChange} {...field} />
                  </Form.Control>
                  <Form.Label>Enable Pagination</Form.Label>
                  <Form.ErrorMessage />
                </Form.Item>
              )
            }}
          />

          {form.watch("paginationEnabled") && (
            <div className="space-y-4">
              <Form.Field
                control={form.control}
                name="requestPage"
                render={({ field }) => {
                  return (
                    <Form.Item>
                      <Form.Label inline>
                        Request Page Field
                        <Form.Control>
                          <Input placeholder="e.g., page, pageNumber..." {...field} className={inlineInputWidth} />
                        </Form.Control>
                      </Form.Label>
                      <Form.ErrorMessage />
                    </Form.Item>
                  )
                }}
              />

              <Form.Field
                control={form.control}
                name="requestLimit"
                render={({ field }) => {
                  return (
                    <Form.Item>
                      <Form.Label inline>
                        Request Limit Field
                        <Form.Control>
                          <Input placeholder="e.g., limit, size..." {...field} className={inlineInputWidth} />
                        </Form.Control>
                      </Form.Label>
                      <Form.ErrorMessage />
                    </Form.Item>
                  )
                }}
              />

              <Form.Field
                control={form.control}
                name="paginationSize"
                render={({ field }) => {
                  return (
                    <Form.Item>
                      <Form.Label inline>
                        Page Size
                        <Form.Control>
                          <Input type="number" placeholder="e.g., 10, 20..." {...field} />
                        </Form.Control>
                      </Form.Label>
                      <Form.ErrorMessage />
                    </Form.Item>
                  )
                }}
              />
            </div>
          )}
        </div>
      </form>
    </Form>
  )
}
