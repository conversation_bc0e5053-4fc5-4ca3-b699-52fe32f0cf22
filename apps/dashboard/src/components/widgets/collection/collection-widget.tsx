import { safQ<PERSON>y } from "@/client"
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/components/common/error-result"
import { Loader } from "@/components/common/loader"
import { showHumanFriendlyError } from "@/lib/is-validation-error"
import { cn } from "@/lib/utils"
import { ChevronLeft, ChevronRight } from "@medusajs/icons"
import { components } from "@saf/sdk"
import { IconButton } from "@saf/ui"
import { IconDatabase } from "@tabler/icons-react"
import { cva, type VariantProps } from "class-variance-authority"
import React, { useState, useMemo } from "react"
import { getNestedProperty, renderLiquidTemplate } from "../utils/binding-utils"

// Utility function to set nested object properties by string path
const setNestedProperty = (obj: any, path: string, value: any): any => {
  if (!path) return obj

  const keys = path.split(".")
  const result = { ...obj }
  let current = result

  for (let i = 0; i < keys.length - 1; i++) {
    const key = keys[i]
    if (!current[key] || typeof current[key] !== "object") {
      current[key] = {}
    } else {
      current[key] = { ...current[key] }
    }
    current = current[key]
  }

  current[keys[keys.length - 1]] = value
  return result
}

const collectionVariants = cva("", {
  variants: {
    style: {
      list: "flex flex-col gap-2",
      grid: "grid gap-3",
    },
    size: {
      default: "",
      compact: "gap-1",
    },
  },
  compoundVariants: [
    {
      style: "grid",
      size: "default",
      className: "grid-cols-1 sm:grid-cols-2",
    },
    {
      style: "grid",
      size: "compact",
      className: "grid-cols-2 sm:grid-cols-3",
    },
  ],
  defaultVariants: {
    style: "list",
    size: "default",
  },
})

const itemVariants = cva("", {
  variants: {
    style: {
      list: "flex items-start gap-3",
      grid: "flex flex-col gap-2",
    },
    designStyle: {
      default: "",
      card: "rounded-md border bg-ui-bg-subtle p-4",
    },
    size: {
      default: "py-3",
      compact: "py-2",
    },
  },
  compoundVariants: [
    {
      designStyle: "card",
      size: "default",
      className: "p-4",
    },
    {
      designStyle: "card",
      size: "compact",
      className: "p-3",
    },
  ],
  defaultVariants: {
    style: "list",
    designStyle: "default",
    size: "default",
  },
})

const imageVariants = cva("flex-shrink-0 overflow-hidden bg-ui-bg-subtle", {
  variants: {
    style: {
      list: "h-12 w-12",
      grid: "h-24 w-full",
    },
    imageShape: {
      circle: "rounded-full",
      square: "rounded-md",
    },
    size: {
      default: "",
      compact: "",
    },
  },
  compoundVariants: [
    {
      style: "list",
      size: "compact",
      className: "h-8 w-8",
    },
    {
      style: "grid",
      size: "compact",
      className: "h-16",
    },
    {
      style: "grid",
      imageShape: "circle",
      className: "h-24 w-24 mx-auto",
    },
    {
      style: "grid",
      imageShape: "circle",
      size: "compact",
      className: "h-16 w-16 mx-auto",
    },
  ],
  defaultVariants: {
    style: "list",
    imageShape: "square",
    size: "default",
  },
})

export interface CollectionWidgetProps extends VariantProps<typeof collectionVariants> {
  items?: any[]
}

const CollectionItem = ({
  item,
  style,
  design,
  itemsData,
}: {
  item: any
  style: "list" | "grid"
  design?: components["schemas"]["CollectionWidget"]["config"]["design"]
  itemsData: components["schemas"]["CollectionWidget"]["config"]["itemsData"]
}) => {
  // Example liquid templates that could be used:
  // title: "{{title | upcase}}" - uppercase the title
  // subtitle: "{{subtitle | truncate: 50}}" - truncate subtitle to 50 chars
  // meta: "{{status | capitalize}} - {{price}} {{currency}}" - combine multiple fields
  // image: "{{image}}?size=200x200" - add query parameters to image URL
  const [renderedData, setRenderedData] = React.useState<{
    title: string
    subtitle: string
    meta: string
    image: string
  }>({
    title: "",
    subtitle: "",
    meta: "",
    image: "",
  })

  // Render liquid templates when item or itemsData changes
  React.useEffect(() => {
    const renderTemplates = async () => {
      try {
        const [title, subtitle, meta, image] = await Promise.all([
          renderLiquidTemplate(itemsData.title, item),
          renderLiquidTemplate(itemsData.subtitle, item),
          renderLiquidTemplate(itemsData.meta, item),
          renderLiquidTemplate(itemsData.image, item),
        ])

        setRenderedData({
          title,
          subtitle,
          meta,
          image,
        })
      } catch (error) {
        console.warn("Failed to render collection item templates:", error)
        // Fallback to direct item properties
        setRenderedData({
          title: item.title || "",
          subtitle: item.subtitle || "",
          meta: item.meta || "",
          image: item.image || "",
        })
      }
    }

    renderTemplates()
  }, [item, itemsData])

  return (
    <div
      className={cn(
        itemVariants({
          style,
          designStyle: design?.style,
          size: design?.size,
        }),
      )}
    >
      {/* Image */}
      {renderedData.image && (
        <div
          className={cn(
            imageVariants({
              style,
              imageShape: design?.imageShape,
              size: design?.size,
            }),
          )}
        >
          <img
            src={renderedData.image}
            alt={renderedData.title || "Collection item"}
            className="h-full w-full object-cover"
            onError={(e) => {
              // Handle broken images gracefully
              const target = e.target as HTMLImageElement
              target.style.display = "none"
              const parent = target.parentElement
              if (parent) {
                parent.innerHTML = `
                  <div class="flex h-full w-full items-center justify-center text-ui-text-muted">
                    <svg class="h-6 w-6" stroke="currentColor" fill="none" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                  </div>
                `
              }
            }}
          />
        </div>
      )}

      {/* Content */}
      <div className={cn("flex min-w-0 flex-1 flex-col gap-1", style === "grid" && "text-center")}>
        {renderedData.meta && <p className="text-ui-text-muted text-xs font-medium uppercase">{renderedData.meta}</p>}
        {renderedData.title && (
          <h4
            className={cn(
              "text-ui-text-base font-semibold leading-tight",
              design?.size === "compact" ? "text-sm" : "text-base",
            )}
          >
            {renderedData.title}
          </h4>
        )}
        {renderedData.subtitle && (
          <p className={cn("text-ui-text-muted", design?.size === "compact" ? "text-xs" : "text-sm")}>
            {renderedData.subtitle}
          </p>
        )}
      </div>
    </div>
  )
}

export const CollectionWidget = ({ data }: { data: components["schemas"]["CollectionWidget"] }) => {
  const { config } = data
  const { style, itemsData, design, options, pagination, rootArrayPath } = config

  // Pagination state management
  const [currentPage, setCurrentPage] = useState(1)

  // Build request body with pagination parameters
  const requestBody = useMemo(() => {
    let body = {}

    if (pagination?.enabled && pagination.requestPage && pagination.requestLimit) {
      const pageSize = pagination.size || 20

      // Set page parameter using the configured field path
      body = setNestedProperty(body, pagination.requestPage, currentPage)

      // Set limit parameter using the configured field path
      body = setNestedProperty(body, pagination.requestLimit, pageSize)
    }

    return body
  }, [pagination, currentPage])

  const {
    data: externalApiResponse,
    isLoading,
    error,
    isError,
  } = safQuery.useQuery(
    "post",
    "/api/customer/external-api/{externalApiId}",
    {
      params: {
        path: {
          externalApiId: config.bindingConfig?.externalApiId || 0,
        },
      },
      body: requestBody,
    },
    {
      enabled: config.bindingConfig?.dataSourceId != null && config.bindingConfig?.externalApiId != null,
    },
  )

  // Extract items from external API response using rootArrayPath if specified
  const extractItemsFromResponse = (responseData: any): any[] => {
    if (!responseData) return []

    // If rootArrayPath is specified, use it to navigate to the correct array
    if (rootArrayPath) {
      const nestedData = getNestedProperty(responseData, rootArrayPath)
      return Array.isArray(nestedData) ? nestedData : []
    }

    // Default behavior: use the data directly
    return Array.isArray(responseData) ? responseData : [responseData]
  }

  const items = extractItemsFromResponse(externalApiResponse?.data)

  // Extract pagination information from API response
  const paginationInfo = useMemo(() => {
    if (!pagination?.enabled || !externalApiResponse?.data) {
      return null
    }

    return {
      currentPage,
      hasPrevPage: currentPage > 1,
      hasNextPage: items.length >= (pagination.size || 20),
    }
  }, [pagination, externalApiResponse?.data, currentPage, items.length])

  // Navigation handlers
  const handlePrevPage = () => {
    if (paginationInfo?.hasPrevPage) {
      setCurrentPage(currentPage - 1)
    }
  }

  const handleNextPage = () => {
    if (paginationInfo?.hasNextPage) {
      setCurrentPage(currentPage + 1)
    }
  }

  // Apply limit if specified (only if pagination is not enabled)
  const limitedItems = !pagination?.enabled && options?.limitItems ? items.slice(0, options.limitItems) : items

  if (isLoading) {
    return (
      <div className="grid place-items-center px-4 py-2">
        <Loader />
      </div>
    )
  }

  if (isError) {
    return (
      <div className="px-4 py-2 text-center">
        <ErrorResult message={showHumanFriendlyError(error)} />
      </div>
    )
  }

  // Handle empty configuration
  if (!itemsData?.title && !itemsData?.subtitle && !itemsData?.meta && !itemsData?.image) {
    return (
      <div className="px-4 py-2">
        <div className="flex items-center justify-center rounded-lg border-2 border-dashed border-ui-border-base bg-ui-bg-subtle p-8 text-center">
          <div className="text-ui-text-muted">
            <IconDatabase className="mx-auto size-8" />
            <p className="mt-2 text-sm">No collection configuration</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="px-4 py-0.5">
      {!limitedItems || limitedItems.length === 0 ? (
        <div className="flex items-center justify-center rounded-lg border border-ui-border-base bg-ui-bg-subtle p-8 text-center">
          <p className="text-sm text-ui-fg-subtle">No items to display</p>
        </div>
      ) : (
        <div
          className={cn(
            collectionVariants({
              style,
              size: design?.size,
            }),
          )}
        >
          {limitedItems.map((item, index) => (
            <CollectionItem key={item.id || index} item={item} style={style} design={design} itemsData={itemsData} />
          ))}
        </div>
      )}

      {pagination?.enabled && paginationInfo && (
        <div className="mt-4 flex items-center justify-center">
          <div className="text-ui-text-muted text-xs">Page {paginationInfo.currentPage}</div>
          <div className="ml-4 flex items-center gap-x-2">
            <IconButton
              type="button"
              variant="secondary"
              size="xsmall"
              disabled={!paginationInfo.hasPrevPage}
              onClick={handlePrevPage}
            >
              <ChevronLeft />
            </IconButton>
            <IconButton
              type="button"
              variant="secondary"
              size="xsmall"
              disabled={!paginationInfo.hasNextPage}
              onClick={handleNextPage}
            >
              <ChevronRight />
            </IconButton>
          </div>
        </div>
      )}
    </div>
  )
}
