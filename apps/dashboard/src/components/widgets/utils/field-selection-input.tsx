import { IconButton, Select, Text } from "@saf/ui"
import { IconChevronDown, IconChevronRight } from "@tabler/icons-react"
import { useMemo, useState } from "react"

type SchemaNode = {
  path: string
  label: string
  children?: SchemaNode[]
  isArray?: boolean
  isObject?: boolean
}

type FieldSelectionInputProps = {
  schema: any
  value?: string
  onChange?: (value: string) => void
  label?: string
  placeholder?: string
  className?: string
  rootArrayPath?: string
}

export const FieldSelectionInput = ({
  schema,
  value,
  onChange,
  label,
  placeholder = "Select a field...",
  className,
  rootArrayPath,
}: FieldSelectionInputProps) => {
  const schemaTree = useMemo(() => parseSchema(schema, rootArrayPath), [schema, rootArrayPath])

  // Convert the raw path to a liquid template format when changing
  const handleValueChange = (rawPath: string) => {
    if (!rawPath) return

    // Format as liquid template: {{path}}
    const liquidTemplate = `{{${rawPath}}}`
    onChange?.(liquidTemplate)
  }

  // Extract the path from liquid template for internal use
  const extractPathFromTemplate = (template?: string): string => {
    if (!template) return ""

    // Extract content between {{ and }}
    const match = template.match(/{{(.*?)}}/)
    return match ? match[1].trim() : template
  }

  const internalValue = extractPathFromTemplate(value)

  // Find the display name for the selected field
  const getDisplayValue = (path: string): string => {
    if (!path) return ""

    // For nested paths (containing dots), show the full path
    if (path.includes(".")) {
      return path
    }

    // For top-level fields, just show the field name
    return path
  }

  const displayValue = getDisplayValue(internalValue)

  return (
    <div className={className}>
      {label && <label className="mb-2 block text-sm font-medium">{label}</label>}
      <Select value={internalValue} onValueChange={handleValueChange}>
        <Select.Trigger>
          <Select.Value>
            {internalValue ? (
              <span className="truncate">{displayValue}</span>
            ) : (
              <span className="text-ui-fg-muted">{placeholder}</span>
            )}
          </Select.Value>
        </Select.Trigger>
        <Select.Content>
          <SchemaTreeView nodes={schemaTree} />
        </Select.Content>
      </Select>
    </div>
  )
}

const SchemaTreeView = ({ nodes }: { nodes: SchemaNode[] }) => {
  return (
    <div className="max-h-[300px] overflow-y-auto p-1">
      {nodes.map((node) => (
        <SchemaTreeNode key={node.path} node={node} />
      ))}
    </div>
  )
}

const SchemaTreeNode = ({ node }: { node: SchemaNode }) => {
  const [expanded, setExpanded] = useState(false)
  const hasChildren = node.children && node.children.length > 0

  return (
    <div className="flex flex-col">
      <div className="flex items-center">
        {hasChildren ? (
          <IconButton
            variant="transparent"
            size="xsmall"
            className="[&>svg]:size-3"
            onClick={() => setExpanded(!expanded)}
          >
            {expanded ? <IconChevronDown /> : <IconChevronRight />}
          </IconButton>
        ) : (
          <div className="w-6" />
        )}
        <Select.Item value={node.path} className="flex-1">
          <div className="flex items-center">
            <span>{node.label}</span>
            {node.isArray && <Text className="ml-1 text-ui-fg-subtle">[...]</Text>}
            {node.isObject && hasChildren && <Text className="ml-1 text-ui-fg-subtle">{"{...}"}</Text>}
          </div>
        </Select.Item>
      </div>
      {expanded && hasChildren && (
        <div className="ml-6">
          {node.children!.map((child) => (
            <SchemaTreeNode key={child.path} node={child} />
          ))}
        </div>
      )}
    </div>
  )
}

// Parse JSON schema into a tree structure
const parseSchema = (schema: any, rootArrayPath?: string): SchemaNode[] => {
  if (!schema) return []

  // If rootArrayPath is provided, navigate to that path first
  if (rootArrayPath && rootArrayPath.trim() !== "") {
    const pathParts = rootArrayPath.split(".")
    let currentSchema = schema

    // Navigate through the path
    for (const part of pathParts) {
      if (!currentSchema) return []

      if (Array.isArray(currentSchema)) {
        // For arrays, use the first item
        currentSchema = currentSchema[0]?.[part]
      } else {
        currentSchema = currentSchema[part]
      }
    }

    // If we've navigated to an array, use its first item
    if (Array.isArray(currentSchema) && currentSchema.length > 0) {
      return parseObjectSchema(currentSchema[0], "")
    } else if (typeof currentSchema === "object" && currentSchema !== null) {
      return parseObjectSchema(currentSchema, "")
    }

    return []
  }

  // Original logic for when no rootArrayPath is provided
  if (Array.isArray(schema)) {
    // Handle array schema
    if (schema.length === 0) return []

    // For arrays, we parse the first item as representative
    const firstItem = schema[0]
    return parseObjectSchema(firstItem, "")
  } else {
    // Handle object schema
    return parseObjectSchema(schema, "")
  }
}

const parseObjectSchema = (obj: any, parentPath: string): SchemaNode[] => {
  if (!obj || typeof obj !== "object") return []

  return Object.entries(obj).map(([key, value]) => {
    const currentPath = parentPath ? `${parentPath}.${key}` : key
    const isObject = value && typeof value === "object" && !Array.isArray(value)
    const isArray = Array.isArray(value)

    let children: SchemaNode[] = []

    if (isObject) {
      children = parseObjectSchema(value, currentPath)
    } else if (isArray && value.length > 0) {
      // For arrays, we parse the first item as representative
      const arrayItemPath = `${currentPath}[0]`
      children = parseObjectSchema(value[0], arrayItemPath)
    }

    return {
      path: currentPath,
      label: key,
      children: children.length > 0 ? children : undefined,
      isArray,
      isObject: isObject && !isArray,
    }
  })
}
